import type {
	APGetCustomFieldType,
	APPostCustomfieldType,
	GetAPAppointmentType,
	GetAPContactType,
	PostAPAppointmentType,
	PostAPContactType,
} from "@type";
import { getConfig, getLoggingConfig } from "@utils/configs";
import { createApiClient, extractResponseData } from "./request";

/**
 * Helper function for conditional logging based on configuration
 * @param message - Log message
 * @param data - Optional data to log when context is enabled
 */
function logInfo(message: string, data?: unknown): void {
	const showContext = getLoggingConfig("showLogContext");
	if (showContext && data) {
		console.log(message, data);
	} else {
		console.log(message);
	}
}

/**
 * Helper function for debug logging based on configuration
 * @param message - Debug message
 * @param data - Optional data to log
 */
function logDebug(message: string, data?: unknown): void {
	const showDebugLogs = getLoggingConfig("showDebugLogs");
	if (showDebugLogs) {
		const showContext = getLoggingConfig("showLogContext");
		if (showContext && data) {
			console.log(`[DEBUG] ${message}`, data);
		} else {
			console.log(`[DEBUG] ${message}`);
		}
	}
}

/**
 * Helper function for standardized API performance logging
 * @param method - HTTP method
 * @param status - HTTP status code
 * @param duration - Duration in milliseconds
 * @param path - Relative API path
 */
function logApiPerformance(method: string, status: number, duration: number, path: string): void {
	const durationSeconds = (duration / 1000).toFixed(1);
	console.log(`[${method}] [${status}] -> ${durationSeconds}s -> ${path}`);
}

/**
 * AP (AutoPatient) API client for patient engagement and communication platform
 *
 * This class provides a comprehensive interface to the AutoPatient (GoHighLevel) API,
 * handling all communication with the patient engagement and communication platform.
 * It implements proper authentication, location-based operations, and error handling
 * for all AP API operations.
 *
 * **Supported Operations:**
 * - Contact management (create, read, update, delete, search)
 * - Appointment/calendar management (create, read, update, delete)
 * - Custom field management (create, read, update)
 * - Note management (create, read, update, delete)
 * - Location-based operations
 *
 * **API Version:**
 * Uses GoHighLevel API v2 (2021-07-28) for all operations.
 * The V1 API has been deprecated and removed from this implementation.
 *
 * **Authentication:**
 * Uses Bearer token authentication with location-specific API key.
 * All requests include the location ID for proper data isolation.
 *
 * **Location Context:**
 * All operations are performed within the context of a specific location
 * as configured in the application settings. This ensures data isolation
 * and proper multi-tenant support.
 *
 * **Error Handling:**
 * - Implements retry logic for transient failures
 * - Handles rate limiting and API quotas
 * - Provides detailed error messages for debugging
 * - Logs all API errors for monitoring
 *
 * **Performance:**
 * - Optimized for Cloudflare Workers environment
 * - Efficient request/response handling
 * - Proper async/await patterns
 * - Connection pooling and reuse
 *
 * @example
 * ```typescript
 * // Get a contact by ID
 * const contact = await apClient.contact.get("contact_123");
 *
 * // Create a new contact
 * const newContact = await apClient.contact.create({
 *   firstName: "John",
 *   lastName: "Doe",
 *   email: "<EMAIL>",
 *   phone: "+1234567890",
 *   tags: ["cc_api"]
 * });
 *
 * // Create an appointment
 * const appointment = await apClient.appointment.create({
 *   contactId: "contact_123",
 *   startTime: "2024-01-01T10:00:00Z",
 *   endTime: "2024-01-01T11:00:00Z",
 *   title: "Consultation"
 * });
 *
 * // Update custom fields
 * await apClient.contact.update("contact_123", {
 *   customFields: [
 *     { id: "field_123", value: "Updated Value" }
 *   ]
 * });
 * ```
 *
 * @see {@link createApiClient} for underlying HTTP client implementation
 * @see {@link extractResponseData} for response parsing utilities
 *
 * @since 1.0.0
 * @version 1.0.0
 */
class APApiClient {
	/**
	 * HTTP client instance configured for AP API v2
	 * @private
	 */
	private client: ReturnType<typeof createApiClient>;

	/**
	 * Creates a new AP API client instance
	 *
	 * Initializes the HTTP client with proper authentication headers, API version,
	 * and base URL from the application configuration. The client is configured
	 * for GoHighLevel API v2 and is ready to use immediately after construction.
	 *
	 * @throws {Error} If AP API domain or key is not configured
	 *
	 * @example
	 * ```typescript
	 * // Client is automatically instantiated as singleton
	 * import { apClient } from '@api/apClient';
	 *
	 * // Use the client directly
	 * const contact = await apClient.contact.get("contact_123");
	 * ```
	 */
	constructor() {
		const apApiDomain = getConfig("apApiDomain");
		const apApiKey = getConfig("apApiKey");

		logDebug(`🔧 Initializing AP API Client - Domain: ${apApiDomain}, Key: ${apApiKey ? 'SET' : 'NOT SET'}`);

		// V2 API client (services.leadconnectorhq.com)
		this.client = createApiClient(apApiDomain as string, {
			Authorization: `Bearer ${apApiKey}`,
			Version: "2021-07-28",
		});

		logDebug(`✅ AP API Client initialized successfully`);
		// Note: V1 API client removed as it's not used in current implementation
	}

	/**
	 * Contact operations for managing AP (AutoPatient) contacts
	 *
	 * Provides comprehensive CRUD operations for contacts in the AutoPatient system,
	 * including creation, retrieval, updates, and deletion with proper error handling.
	 */
	contact = {
		/**
		 * Retrieves a contact by their unique ID from the AP system
		 *
		 * @param id - Unique contact identifier in AP system
		 * @returns Promise resolving to complete contact data
		 *
		 * @throws {ApiError} When contact is not found or API request fails
		 *
		 * @example
		 * ```typescript
* try {
		 *   const contact = await apClient.contact.get("contact_123");
		 *   console.log(`Contact: ${contact.firstName} ${contact.lastName}`);
		 * } catch (error) {
		 *   console.error("Failed to fetch contact:", error.message);
		 * }
		 *
```
		 */
		get: async (id: string): Promise<GetAPContactType> => {
			const startTime = Date.now();
			logDebug(`🔍 AP API: Getting contact - ID: ${id}`);

			const response = await this.client<{ contact: GetAPContactType }>({
				url: `/contacts/${id}`,
				method: "GET",
			});

			const duration = Date.now() - startTime;
			logApiPerformance("GET", response.status, duration, `/contacts/${id}`);

			const result = extractResponseData(response, "contact") as GetAPContactType;
			logDebug(`✅ AP API: Contact retrieved successfully - ${result.firstName} ${result.lastName}`);
			return result;
		},

		/**
		 * Creates a new contact in the AP system with location association
		 *
		 * Automatically associates the contact with the configured location ID
		 * and handles proper data validation and error responses.
		 *
		 * @param data - Contact data to create (without locationId, added automatically)
		 * @returns Promise resolving to the created contact with AP-generated ID
		 *
		 * @throws {ApiError} When contact creation fails due to validation or API errors
		 *
		 * @example
		 * ```typescript
* const newContact = await apClient.contact.create({
		 *   firstName: "John",
		 *   lastName: "Doe",
		 *   email: "<EMAIL>",
		 *   phone: "+1234567890"
		 * });
		 * console.log(`Created contact with ID: ${newContact.id}`);
		 *
```
		 */
		create: async (data: PostAPContactType): Promise<GetAPContactType> => {
			const startTime = Date.now();
			const locationID = getConfig("locationID");
			logDebug(`👤 AP API: Creating contact - ${data.firstName} ${data.lastName} (${data.email})`);

			const response = await this.client<{ contact: GetAPContactType }>({
				url: "/contacts/",
				method: "POST",
				data: { ...data, locationId: locationID },
			});

			const duration = Date.now() - startTime;
			logApiPerformance("POST", response.status, duration, "/contacts/");

			const result = extractResponseData(response, "contact") as GetAPContactType;
			logDebug(`✅ AP API: Contact created successfully - ID: ${result.id}`);
			return result;
		},

		/**
		 * Update a contact
		 * @param id - Contact ID
		 * @param data - Contact data to update
		 * @returns Updated contact
		 */
		update: async (
			id: string,
			data: PostAPContactType,
		): Promise<GetAPContactType> => {
			const startTime = Date.now();
			logDebug(`📝 AP API: Updating contact - ID: ${id}, Name: ${data.firstName} ${data.lastName}`);

			const response = await this.client<{ contact: GetAPContactType }>({
				url: `/contacts/${id}`,
				method: "PUT",
				data,
			});

			const duration = Date.now() - startTime;
			logApiPerformance("PUT", response.status, duration, `/contacts/${id}`);

			const result = extractResponseData(response, "contact") as GetAPContactType;
			logDebug(`✅ AP API: Contact updated successfully - ID: ${result.id}`);
			return result;
		},

		/**
		 * Upsert a contact (create or update)
		 * @param data - Contact data
		 * @returns Contact data
		 */
		upsert: async (data: PostAPContactType): Promise<GetAPContactType> => {
			const startTime = Date.now();
			const locationID = getConfig("locationID");
			logDebug(`🔄 AP API: Upserting contact - ${data.firstName} ${data.lastName} (${data.email})`);

			const response = await this.client<{ contact: GetAPContactType }>({
				url: "/contacts/upsert/",
				method: "POST",
				data: { ...data, locationId: locationID },
			});

			const duration = Date.now() - startTime;
			logApiPerformance("POST", response.status, duration, "/contacts/upsert/");

			const result = extractResponseData(response, "contact") as GetAPContactType;
			logDebug(`✅ AP API: Contact upserted successfully - ID: ${result.id}`);
			return result;
		},

		/**
		 * Delete a contact
		 * @param id - Contact ID
		 */
		delete: async (id: string): Promise<void> => {
			await this.client({
				url: `/contacts/${id}/`,
				method: "DELETE",
			});
		},

		/**
		 * Get contact appointments
		 * @param contactId - Contact ID
		 * @returns Appointments data
		 */
		appointments: async (
			contactId: string,
		): Promise<GetAPAppointmentType[]> => {
			const response = await this.client<GetAPAppointmentType[]>({
				url: `/contacts/${contactId}/appointments/`,
				method: "GET",
			});
			return response.data;
		},

		/**
		 * Get all contacts
		 * @param params - Query parameters
		 * @returns Array of contacts
		 */
		all: async (params?: {
			limit?: number;
			query?: string;
			startAfter?: number;
			startAfterId?: string;
		}): Promise<GetAPContactType[]> => {
			const locationID = getConfig("locationID") as string;
			const queryParams: Record<string, string> = { locationId: locationID };

			if (params?.limit) queryParams.limit = params.limit.toString();
			if (params?.query) queryParams.query = params.query;
			if (params?.startAfter)
				queryParams.startAfter = params.startAfter.toString();
			if (params?.startAfterId) queryParams.startAfterId = params.startAfterId;

			const response = await this.client<{ contacts: GetAPContactType[] }>({
				url: "/contacts/",
				method: "GET",
				params: queryParams,
			});
			return extractResponseData(response, "contacts");
		},
	};

	/**
	 * Appointment operations
	 */
	appointment = {
		/**
		 * Get an appointment by ID
		 * @param apId - Appointment ID
		 * @returns Appointment data
		 */
		get: async (apId: string): Promise<GetAPAppointmentType> => {
			const response = await this.client<{ appointment: GetAPAppointmentType }>(
				{
					url: `/calendars/events/appointments/${apId}`,
					method: "GET",
				},
			);
			return extractResponseData(response, "appointment");
		},

		/**
		 * Create a new appointment
		 * @param payload - Appointment data
		 * @returns Created appointment
		 */
		create: async (
			payload: PostAPAppointmentType,
		): Promise<GetAPAppointmentType> => {
			const locationID = getConfig("locationID");
			const apCalendarId = getConfig("apCalendarId");
			const response = await this.client<GetAPAppointmentType>({
				url: "/calendars/events/appointments",
				method: "POST",
				data: {
					calendarId: apCalendarId,
					...payload,
					locationId: locationID,
				},
			});
			return response.data;
		},

		/**
		 * Create a block slot
		 * @param payload - Block slot data
		 * @returns Created block slot
		 */
		createBlockSlot: async (
			payload: PostAPAppointmentType,
		): Promise<GetAPAppointmentType> => {
			const locationID = getConfig("locationID");
			const apCalendarId = getConfig("apCalendarId");
			const response = await this.client<GetAPAppointmentType>({
				url: "/calendars/events/block-slots",
				method: "POST",
				data: {
					calendarId: apCalendarId,
					...payload,
					locationId: locationID,
				},
			});
			return response.data;
		},

		/**
		 * Update an appointment
		 * @param apId - Appointment ID
		 * @param payload - Appointment data to update
		 * @returns Updated appointment
		 */
		update: async (
			apId: string,
			payload: Partial<PostAPAppointmentType>,
		): Promise<GetAPAppointmentType> => {
			const response = await this.client<{ appointment: GetAPAppointmentType }>(
				{
					url: `/calendars/events/appointments/${apId}`,
					method: "PUT",
					data: payload,
				},
			);
			return extractResponseData(response, "appointment");
		},

		/**
		 * Delete an appointment
		 * @param apId - Appointment ID
		 */
		delete: async (apId: string): Promise<void> => {
			await this.client({
				url: `/calendars/events/appointments/${apId}`,
				method: "DELETE",
			});
		},
	};

	/**
	 * Custom field operations
	 */
	customField = {
		/**
		 * Get all custom fields
		 * @returns Array of custom fields
		 */
		all: async (): Promise<APGetCustomFieldType[]> => {
			const locationID = getConfig("locationID") as string;
			const response = await this.client<{
				customFields: APGetCustomFieldType[];
			}>({
				url: `/locations/${locationID}/customFields`,
				method: "GET",
			});
			return extractResponseData(response, "customFields");
		},

		/**
		 * Create a custom field
		 * @param data - Custom field data
		 * @returns Created custom field
		 */
		create: async (
			data: APPostCustomfieldType,
		): Promise<APGetCustomFieldType> => {
			const locationID = getConfig("locationID") as string;
			const response = await this.client<{ customField: APGetCustomFieldType }>(
				{
					url: `/locations/${locationID}/customFields`,
					method: "POST",
					data: { ...data, model: data.model || "contact" },
				},
			);
			return extractResponseData(response, "customField");
		},

		/**
		 * Update a custom field
		 * @param id - Custom field ID
		 * @param data - Custom field data to update
		 * @returns Updated custom field
		 */
		update: async (
			id: string,
			data: Partial<APPostCustomfieldType>,
		): Promise<APGetCustomFieldType> => {
			const locationID = getConfig("locationID") as string;
			const response = await this.client<{ customField: APGetCustomFieldType }>(
				{
					url: `/locations/${locationID}/customFields/${id}`,
					method: "PUT",
					data,
				},
			);
			return extractResponseData(response, "customField");
		},

		/**
		 * Delete a custom field
		 * @param id - Custom field ID
		 */
		delete: async (id: string): Promise<void> => {
			const locationID = getConfig("locationID") as string;
			await this.client({
				url: `/locations/${locationID}/customFields/${id}`,
				method: "DELETE",
			});
		},
	};

	/**
	 * Note operations
	 */
	note = {
		/**
		 * Create a note for a contact
		 * @param contactId - Contact ID
		 * @param body - Note content
		 * @returns Created note
		 */
		create: async (
			contactId: string,
			body: string,
		): Promise<{
			id: string;
			body: string;
			contactId: string;
			dateAdded: string;
		}> => {
			const response = await this.client<{
				id: string;
				body: string;
				contactId: string;
				dateAdded: string;
			}>({
				url: `/contacts/${contactId}/notes/`,
				method: "POST",
				data: { body },
			});
			return response.data;
		},

		/**
		 * Update a note
		 * @param contactId - Contact ID
		 * @param noteId - Note ID
		 * @param body - Updated note content
		 * @returns Updated note
		 */
		update: async (
			contactId: string,
			noteId: string,
			body: string,
		): Promise<{
			id: string;
			body: string;
			contactId: string;
			dateUpdated: string;
		}> => {
			const response = await this.client<{
				id: string;
				body: string;
				contactId: string;
				dateUpdated: string;
			}>({
				url: `/contacts/${contactId}/notes/${noteId}`,
				method: "PUT",
				data: { body },
			});
			return response.data;
		},

		/**
		 * Delete a note
		 * @param contactId - Contact ID
		 * @param noteId - Note ID
		 * @returns Success status
		 */
		delete: async (contactId: string, noteId: string): Promise<boolean> => {
			try {
				await this.client({
					url: `/contacts/${contactId}/notes/${noteId}`,
					method: "DELETE",
				});
				return true;
			} catch (error) {
				console.error(`Failed to delete note ${noteId}:`, error);
				return false;
			}
		},
	};
}

// Export singleton instance
export const apClient = new APApiClient();
