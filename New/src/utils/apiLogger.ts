import { logger } from "@utils/logger";
import { getLoggingConfig } from "@utils/configs";
import type { RequestOptions } from "@api/request";

/**
 * Helper function to extract relative path from full URL
 * @param fullUrl - Complete URL
 * @returns Relative path only
 */
function getRelativePath(fullUrl: string): string {
	try {
		const url = new URL(fullUrl);
		return url.pathname + url.search;
	} catch {
		// If URL parsing fails, return the original string
		return fullUrl;
	}
}

/**
 * Helper function for standardized API performance logging
 * @param method - HTTP method
 * @param status - HTTP status code
 * @param duration - Duration in milliseconds
 * @param fullUrl - Full URL (will be converted to relative path)
 */
function logApiPerformance(method: string, status: number, duration: number, fullUrl: string): void {
	const durationSeconds = (duration / 1000).toFixed(1);
	const relativePath = getRelativePath(fullUrl);
	console.log(`[${method}] [${status}] -> ${durationSeconds}s -> ${relativePath}`);
}

/**
 * API Logging Utilities for DermaCare Webhook System
 * 
 * Provides comprehensive logging for API requests and responses to help with
 * debugging connectivity issues, authentication problems, and payload validation
 * errors during webhook testing.
 * 
 * **Features:**
 * - Location ID logging for AP API requests
 * - Secure API token masking (first 10 + last 10 characters)
 * - Complete URL logging with query parameters
 * - Request payload logging for POST/PUT/PATCH requests
 * - Request/response correlation IDs for easier debugging
 * - Appropriate log levels (info for normal operations, debug for detailed payloads)
 * - Cloudflare Workers compatible
 * 
 * @since 1.0.0
 * @version 1.0.0
 */

/**
 * API types for logging context
 */
export type ApiType = "CC_API" | "AP_API" | "UNKNOWN";

/**
 * Request timing information
 */
export interface RequestTiming {
	startTime: number;
	endTime?: number;
	duration?: number;
}

/**
 * API request context for logging
 */
export interface ApiRequestContext {
	correlationId: string;
	apiType: ApiType;
	baseUrl: string;
	fullUrl: string;
	method: string;
	locationId?: string;
	maskedAuthToken?: string;
	timing: RequestTiming;
}

/**
 * Masks an API token for secure logging
 * 
 * Shows only the first 10 and last 10 characters of the token,
 * masking the middle portion for security.
 * 
 * @param token - The API token to mask
 * @returns Masked token string
 * 
 * @example
 * ```typescript
 * const masked = maskApiToken("abcdefghijklmnopqrstuvwxyz1234567890");
 * // Returns: "abcdefghij***567890"
 * ```
 */
export function maskApiToken(token: string): string {
	if (!token || typeof token !== "string") {
		return "***INVALID_TOKEN***";
	}

	// For very short tokens, mask completely
	if (token.length <= 20) {
		return "***MASKED***";
	}

	// Show first 10 and last 10 characters
	return `${token.slice(0, 10)}***${token.slice(-10)}`;
}

/**
 * Generates a unique correlation ID for request tracking
 * 
 * Creates a unique identifier that can be used to correlate
 * request and response logs for easier debugging.
 * 
 * @returns Unique correlation ID
 * 
 * @example
 * ```typescript
 * const correlationId = generateCorrelationId();
 * // Returns: "req_1703123456789_abc123def"
 * ```
 */
export function generateCorrelationId(): string {
	const timestamp = Date.now();
	const randomSuffix = Math.random().toString(36).substring(2, 11);
	return `req_${timestamp}_${randomSuffix}`;
}

/**
 * Determines the API type based on the base URL
 * 
 * @param baseUrl - The base URL of the API
 * @returns API type (CC_API, AP_API, or UNKNOWN)
 */
export function determineApiType(baseUrl: string): ApiType {
	if (!baseUrl || typeof baseUrl !== "string") {
		return "UNKNOWN";
	}

	const url = baseUrl.toLowerCase();
	
	if (url.includes("clinicore")) {
		return "CC_API";
	}
	
	if (url.includes("leadconnectorhq")) {
		return "AP_API";
	}
	
	return "UNKNOWN";
}

/**
 * Extracts location ID from request data or parameters
 * 
 * @param options - Request options containing data and params
 * @returns Location ID if found, undefined otherwise
 */
export function extractLocationId(options: RequestOptions): string | undefined {
	// Check request data for locationId
	if (options.data && typeof options.data === "object" && options.data !== null) {
		const data = options.data as Record<string, unknown>;
		if (data.locationId && typeof data.locationId === "string") {
			return data.locationId;
		}
	}

	// Check query parameters for locationId
	if (options.params && options.params.locationId) {
		return options.params.locationId;
	}

	return undefined;
}

/**
 * Extracts and masks authorization token from headers
 * 
 * @param headers - Request headers
 * @returns Masked authorization token if found
 */
export function extractMaskedAuthToken(headers: Record<string, string>): string | undefined {
	const authHeader = headers.Authorization || headers.authorization;
	
	if (!authHeader || typeof authHeader !== "string") {
		return undefined;
	}

	// Extract token from "Bearer <token>" format
	const bearerMatch = authHeader.match(/^Bearer\s+(.+)$/i);
	if (bearerMatch && bearerMatch[1]) {
		return maskApiToken(bearerMatch[1]);
	}

	// If not Bearer format, mask the entire header value
	return maskApiToken(authHeader);
}

/**
 * Creates API request context for logging
 * 
 * @param baseUrl - Base URL for the API
 * @param options - Request options
 * @param headers - Final request headers
 * @returns API request context
 */
export function createApiRequestContext(
	baseUrl: string,
	options: RequestOptions,
	headers: Record<string, string>
): ApiRequestContext {
	const correlationId = generateCorrelationId();
	const apiType = determineApiType(baseUrl);
	
	// Build full URL
	const url = new URL(options.url || "", baseUrl);
	if (options.params) {
		Object.entries(options.params).forEach(([key, value]) => {
			if (value !== null && value !== undefined) {
				url.searchParams.append(key, String(value));
			}
		});
	}

	const context: ApiRequestContext = {
		correlationId,
		apiType,
		baseUrl,
		fullUrl: url.toString(),
		method: options.method,
		timing: {
			startTime: Date.now(),
		},
	};

	// Extract location ID for AP API requests
	if (apiType === "AP_API") {
		context.locationId = extractLocationId(options);
	}

	// Extract and mask auth token
	context.maskedAuthToken = extractMaskedAuthToken(headers);

	return context;
}

/**
 * Logs API request details before sending the request
 * 
 * @param context - API request context
 * @param options - Request options (for payload logging)
 */
export function logApiRequest(context: ApiRequestContext, options: RequestOptions): void {
	const showDebugLogs = getLoggingConfig("showDebugLogs");
	const showLogContext = getLoggingConfig("showLogContext");

	if (!showDebugLogs) {
		return; // Skip all request logging when debug logs are disabled
	}

	const { correlationId, apiType, fullUrl, method, locationId, maskedAuthToken } = context;
	const relativePath = getRelativePath(fullUrl);

	// Log basic request information
	if (showLogContext) {
		logger.info(
			`🚀 ${apiType} Request: ${method} ${relativePath}`,
			{
				correlationId,
				apiType,
				method,
				url: relativePath,
				locationId,
				authToken: maskedAuthToken,
			}
		);
	} else {
		logger.info(`🚀 ${apiType} Request: ${method} ${relativePath}`);
	}

	// Log request payload for POST/PUT/PATCH requests (debug level)
	if (
		options.data &&
		options.method &&
		["POST", "PUT", "PATCH"].includes(options.method)
	) {
		if (showLogContext) {
			logger.debug(
				`📤 ${apiType} Request Payload`,
				{
					correlationId,
					method,
					payload: options.data,
				}
			);
		} else {
			logger.debug(`📤 ${apiType} Request Payload`);
		}
	}
}

/**
 * Logs API response details after receiving the response
 * 
 * @param context - API request context (will be mutated to add timing)
 * @param status - HTTP status code
 * @param success - Whether the request was successful
 * @param responseSize - Optional response size in bytes
 */
export function logApiResponse(
	context: ApiRequestContext,
	status: number,
	success: boolean,
	responseSize?: number
): void {
	// Update timing
	context.timing.endTime = Date.now();
	context.timing.duration = context.timing.endTime - context.timing.startTime;

	const { correlationId, apiType, method, fullUrl, timing } = context;
	const showDebugLogs = getLoggingConfig("showDebugLogs");
	const showLogContext = getLoggingConfig("showLogContext");

	// Always log standardized performance format
	logApiPerformance(method, status, timing.duration || 0, fullUrl);

	// Only log detailed response info when debug logs are enabled
	if (showDebugLogs) {
		const statusEmoji = success ? "✅" : "❌";
		const relativePath = getRelativePath(fullUrl);

		if (showLogContext) {
			logger.info(
				`${statusEmoji} ${apiType} Response: ${status} (${timing.duration}ms)`,
				{
					correlationId,
					apiType,
					method,
					url: relativePath,
					status,
					success,
					duration: timing.duration,
					responseSize,
				}
			);
		} else {
			logger.info(`${statusEmoji} ${apiType} Response: ${status} (${timing.duration}ms)`);
		}
	}
}

/**
 * Logs API error details with correlation context
 * 
 * @param context - API request context
 * @param error - The error that occurred
 * @param attempt - Current retry attempt number
 * @param maxAttempts - Maximum number of attempts
 */
export function logApiError(
	context: ApiRequestContext,
	error: Error,
	attempt: number,
	maxAttempts: number
): void {
	// Update timing if not already set
	if (!context.timing.endTime) {
		context.timing.endTime = Date.now();
		context.timing.duration = context.timing.endTime - context.timing.startTime;
	}

	const { correlationId, apiType, method, fullUrl, timing } = context;
	const showLogContext = getLoggingConfig("showLogContext");
	const relativePath = getRelativePath(fullUrl);
	const isRetrying = attempt < maxAttempts;
	const retryInfo = isRetrying ? ` (attempt ${attempt}/${maxAttempts}, retrying...)` : ` (final attempt ${attempt}/${maxAttempts})`;

	// Extract status code from ApiError if available
	const status = (error as any)?.status || 'ERR';

	// Always log standardized performance format for failed API calls
	logApiPerformance(method, status, timing.duration || 0, fullUrl);

	// Enhanced error logging for failed API calls
	console.log(`💥 API Error Details:`);
	console.log(`   Method: ${method}`);
	console.log(`   Status: ${status}`);
	console.log(`   Duration: ${((timing.duration || 0) / 1000).toFixed(1)}s`);
	console.log(`   Endpoint: ${relativePath}`);
	console.log(`   Error: ${error.message}`);

	// Include response body if available from ApiError
	if ((error as any)?.response) {
		console.log(`   Response: ${JSON.stringify((error as any).response, null, 2)}`);
	}

	// Always log API errors, but conditionally include context
	if (showLogContext) {
		logger.error(
			`💥 ${apiType} Error: ${error.message}${retryInfo}`,
			{
				correlationId,
				apiType,
				method,
				url: relativePath,
				error: error.message,
				errorName: error.name,
				attempt,
				maxAttempts,
				duration: timing.duration,
				isRetrying,
				status,
				responseBody: (error as any)?.response,
			}
		);
	} else {
		logger.error(`💥 ${apiType} Error: ${error.message}${retryInfo}`);
	}
}
